/**
 * @file gps_serial_test.c
 * @brief Standalone GPS serial communication test
 * @note This is a minimal test program to verify GPS serial communication
 *       without running the full VMS system. Tests LPUART communication,
 *       command sending, and data reception.
 */

#include "pelagic.h"
#include "nmea.h"
#include "gps.h"
#include "console.h"
#include "gpio_api.h"
#include "serial_api_stubs.h"

enum {
        GPS_TX_BUFFER_SIZE            = 32,     /**< Bytes for transmit buffer */
        GPS_RX_BUFFER_SIZE            = 128,    /**< Bytes for receive buffer */
};

// turn on only the second sentence (GPRMC)
#define PMTK_SET_NMEA_OUTPUT_RMCONLY "PMTK314,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0"
// turn on GPRMC and GGA
#define PMTK_SET_NMEA_OUTPUT_RMCGGA "PMTK314,0,1,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0"

// turn on RMC, GGA and GSA
#define PMTK_SET_NMEA_OUTPUT_RMCGGAGSA "PMTK314,0,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0"

// turn on ALL THE DATA
#define PMTK_SET_NMEA_OUTPUT_ALLDATA "PMTK314,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0"
// turn off output
#define PMTK_SET_NMEA_OUTPUT_OFF "PMTK314,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0"

#define PMTK_SET_BACKUP_MODE    "PMTK225,4"

#define PMTK_FULL_MODE          "PMTK225,0"   // Turn on full mode
#define PMTK_ALWAYS_LOCATE_MODE "PMTK225,8"   // Turn on Always Locate standby mode
#define PMTK_PERIODIC_COMMAND "PMTK225,2,%d,%d"

// GPS globals that need to be defined for this test
char gps_data[GPS_DATA_SIZE];
char gps_op[256];
nmea_t gps_loc;

// External references to globals defined in other files
extern bool gps_initted;
extern serial_t gps_port;
extern gpio_t gps_en_pin;
extern int8_t serial_semihost;
extern serial_t console_uart;

// Missing globals that need to be defined for this test
volatile bool gps_show_bytes = false;

// Flash store stubs (needed by event.c) - match actual function signatures
bool flash_store_lock(flash_partition_t *part, uint32_t timeout) { return true; }
void flash_store_unlock(flash_partition_t *part) {}
bool flash_store_partial_okay(flash_partition_t *part, uint32_t bytes) { return true; }
flash_result_t flash_store_write(flash_partition_t *part, uint8_t *data, uint32_t bytes) { return FLASH_SUCCESS; }
flash_partition_t event_partition;

// Forward declarations for GPS functions
bool gps_send_command(const char *command, const char *name);

// Remove the conflicting rtc_read stub since rtc_api.c is now included

/**
 * @brief Quick GPS serial communication test
 * @return true if GPS responds to commands, false if communication fails
 */
bool gps_test_serial_communication(void)
{
    printf("GPS Serial Test: Starting...\n");
    
    // Initialize GPS if not already done
    if (!gps_initted) {
        gps_init();
    }
    
    // Power on GPS and reset
    printf("GPS Serial Test: Powering on and resetting GPS...\n");
    serial_power_on(&gps_port);
    gps_reset();
    
    // Test 1: Try to send a simple command and get response
    printf("GPS Serial Test: Testing basic command response...\n");
    bool cmd_success = gps_send_command(PMTK_SET_NMEA_OUTPUT_RMCGGAGSA, "test-rmc-gga-gsa");
    
    if (cmd_success) {
        printf("GPS Serial Test: ✓ Command acknowledged successfully\n");
    } else {
        printf("GPS Serial Test: ✗ Command failed or no response\n");
    }
    
    // Test 2: Try to read raw data for a few seconds
    printf("GPS Serial Test: Reading raw GPS data for 5 seconds...\n");
    printf("GPS Serial Test: Showing LPUART buffer status before reading...\n");
    lpuart_debug_buffer_status();

    uint32_t start_time = rtc_read();
    int total_bytes = 0;
    int read_attempts = 0;

    while ((rtc_read() - start_time) < 5) {
        printf("GPS Serial Test: Attempt %d - buffer status before read:\n", read_attempts + 1);
        lpuart_debug_buffer_status();

        int bytes = serial_read_timeout(&gps_port, gps_data, GPS_DATA_SIZE, 1000);
        read_attempts++;

        printf("GPS Serial Test: Attempt %d - buffer status after read:\n", read_attempts);
        lpuart_debug_buffer_status();

        if (bytes > 0) {
            total_bytes += bytes;
            printf("GPS Serial Test: Read %d bytes (attempt %d)\n", bytes, read_attempts);

            // Show first few characters for debugging
            printf("GPS Serial Test: Data sample: ");
            for (int i = 0; i < bytes && i < 40; i++) {
                char c = gps_data[i];
                if (c >= 32 && c <= 126) {
                    printf("%c", c);
                } else if (c == '\r') {
                    printf("\\r");
                } else if (c == '\n') {
                    printf("\\n");
                } else {
                    printf("\\x%02X", (unsigned char)c);
                }
            }
            printf("\n");
        } else {
            printf("GPS Serial Test: No data received (attempt %d)\n", read_attempts);
        }

        // Small delay between attempts to let data accumulate
        osDelay(100);
    }
    
    printf("GPS Serial Test: Total bytes received: %d in %d attempts\n", total_bytes, read_attempts);
    
    // Test 3: Check if we can parse any NMEA sentences
    printf("GPS Serial Test: Testing NMEA parsing...\n");
    nmea_init(&gps_loc);
    bool found_nmea = false;
    
    start_time = rtc_read();
    while ((rtc_read() - start_time) < 3 && !found_nmea) {
        int bytes = serial_read_timeout(&gps_port, gps_data, GPS_DATA_SIZE, 1000);
        
        if (bytes > 0) {
            for (int i = 0; i < bytes; i++) {
                nmea_result_t result = nmea_fusedata(&gps_loc, gps_data[i]);
                if (result != NMEA_NONE && result != NMEA_CRC_ERR) {
                    printf("GPS Serial Test: ✓ Found valid NMEA sentence type %d\n", result);
                    found_nmea = true;
                    break;
                }
            }
        }
    }
    
    if (!found_nmea) {
        printf("GPS Serial Test: ✗ No valid NMEA sentences found\n");
    }
    
    // Summary
    bool overall_success = cmd_success && (total_bytes > 0);
    printf("GPS Serial Test: %s\n", overall_success ? "✓ PASSED" : "✗ FAILED");
    printf("GPS Serial Test: - Command response: %s\n", cmd_success ? "OK" : "FAIL");
    printf("GPS Serial Test: - Data reception: %s (%d bytes)\n", 
           total_bytes > 0 ? "OK" : "FAIL", total_bytes);
    printf("GPS Serial Test: - NMEA parsing: %s\n", found_nmea ? "OK" : "FAIL");
    
    return overall_success;
}

/**
 * @brief Test GPS thread functionality in isolation
 */
void test_gps_thread_subset(void)
{
    printf("GPS Thread Test: Starting GPS thread subset test...\n");
    
    // Initialize GPS
    gps_init();
    serial_power_on(&gps_port);
    
    // Test GPS configuration sequence (similar to what gps_thread does)
    printf("GPS Thread Test: Configuring GPS...\n");
    gps_reset();
    gps_highres_mode();
    
    // Test reading GPS data in a loop (like gps_thread main loop)
    printf("GPS Thread Test: Reading GPS data for 10 seconds...\n");
    nmea_init(&gps_loc);
    
    uint32_t start_time = rtc_read();
    int nmea_sentences = 0;
    int total_chars = 0;
    char fix_quality = '0'; // assume no fix initially
    
    while ((rtc_read() - start_time) < 10) {
        // Simulate the gps_thread main loop
        int bytes = serial_read_timeout(&gps_port, gps_data, GPS_DATA_SIZE, 1000);
        
        if (bytes > 0) {
            total_chars += bytes;
            
            // Process each character through NMEA parser
            for (int i = 0; i < bytes; i++) {
                nmea_result_t result = nmea_fusedata(&gps_loc, gps_data[i]);
                if (result != NMEA_NONE && result != NMEA_CRC_ERR) {
                    nmea_sentences++;
                    printf("GPS Thread Test: Parsed NMEA sentence %d (type %d)\n", 
                           nmea_sentences, result);
                    
                    // Show location if we have a fix
                    fix_quality = gps_loc.words[6][0];
                    if (fix_quality != '0') {
                        printf("GPS Thread Test: Fix quality: %c, Satellites: %d\n",
                               fix_quality, gps_loc.satellites_used);
                        printf("GPS Thread Test: Lat: %f, Lon: %f\n",
                               gps_loc.latitude, gps_loc.longitude);
                    }
                    else {
                        printf("GPS Thread Test: No fix\n");
                    }
                }
            }
        }
    }
    
    printf("GPS Thread Test: Processed %d characters, %d NMEA sentences\n", 
           total_chars, nmea_sentences);
    printf("GPS Thread Test: Final fix quality: %d\n", fix_quality);
}

/**
 * @brief Main test program
 */
int main(void)
{
    char  *buffer;
    int bytes;
    char op[4];
    uint8_t checksum = 0;
    char *command = PMTK_SET_NMEA_OUTPUT_RMCGGAGSA;

    // Basic system initialization
    printf("GPS Test Program Starting...\n");

    printf("=== Pin Setup Check ===\n");
    gps_init(); // calls gpio_init, serial_init, pin_setup()
                // expect pins to have initial values
    assert_param(gpio_read(&gps_vbckp_pin) == 1);   // rtc backup domain, high
    assert_param(gpio_read(&gps_en_pin) == 0);      // main power, low
    assert_param(gpio_read(&gps_reset_pin) == 1);   // active low reset pin, high

    printf("=== Startup Test ===\n");
    // gps_reset(); // too much code here!
    gps_power_on(); // set main power on
    assert_param(gpio_read(&gps_vbckp_pin) == 1);   // rtc backup domain, high
    assert_param(gpio_read(&gps_en_pin) == 1);      // main power, HIGH
    assert_param(gpio_read(&gps_reset_pin) == 1);   // active low reset pin, high

    uint32_t started = rtc_read();
    printf("gps: send[%s] [%s]\n", "RMCGGAGSA", command);
    for (const char *str = command; *str; str++)
        checksum = checksum ^ (uint8_t) *str;
    bytes = sprintf(gps_data, "$%s*%2.2X\r\n", command, checksum);
    serial_write(&gps_port, gps_data, bytes);
    // Don't wait for ack.. haven't figured that out yet

    printf("=== GPS Serial Read Test ===\n");
    // Try to read ANY data without faulting
    for (;;) {
        bytes = serial_read_timeout(&gps_port, gps_data, GPS_DATA_SIZE, 1000);
        if (bytes == 0) {
            printf("No data received\n");
            break;
        }
        printf("Received %d bytes: ", bytes);
        for (int i = 0; i < bytes; i++) {
            printf("%c", gps_data[i]);
        }
        printf("\n");
    }
    
    printf("=== GPS Serial Communication Test ===\n");
    bool serial_ok = gps_test_serial_communication();
    
    printf("\n=== GPS Thread Subset Test ===\n");
    test_gps_thread_subset();
    
    printf("\n=== Test Summary ===\n");
    printf("Serial Communication: %s\n", serial_ok ? "PASSED" : "FAILED");
    printf("GPS Test Program Complete.\n");
    
    return serial_ok ? 0 : 1;
}
